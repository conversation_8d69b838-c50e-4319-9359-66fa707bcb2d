import Foundation

// MARK: - Merge Policy

enum OverlapMergePolicy {
    case replaceNonFavorites
    case appendAll
}

// MARK: - PlanStore v1 (UserDefaults JSON)
// Stores last Quick generation and last Meal Prep plan per PRD v3 Phase 2

@MainActor
final class PlanStore {
    static let shared = PlanStore()
    private let defaults = UserDefaults.standard

    private let quickKey = "planstore.lastQuick.v1"
    private let mealPrepKey = "planstore.lastMealPrep.v1"

    private let encoder: JSONEncoder = {
        let e = JSONEncoder()
        e.dateEncodingStrategy = .iso8601
        return e
    }()
    private let decoder: JSONDecoder = {
        let d = JSONDecoder()
        d.dateDecodingStrategy = .iso8601
        return d
    }()

    // MARK: - Public API

    func saveLastQuick(config: QuickConfiguration, recipes: [RecipeUIModel]) {
        let model = LastQuick(
            mealType: config.mealType,
            numberOfDishes: config.numberOfDishes,
            totalCookTime: config.totalTimeMinutes,
            cuisines: config.cuisines,
            additionalRequest: config.additionalRequest.isEmpty ? nil : config.additionalRequest,
            generatedAt: Date(),
            recipes: recipes
        )
        if let data = try? encoder.encode(model) {
            defaults.set(data, forKey: quickKey)
        }
    }

    func loadLastQuick() -> LastQuick? {
        guard let data = defaults.data(forKey: quickKey) else { return nil }
        return try? decoder.decode(LastQuick.self, from: data)
    }

    func saveLastMealPrep(config: CustomConfiguration, recipes: [RecipeUIModel]) {
        let plan = MealPlanBuilder.buildPlan(from: config, recipes: recipes)
        let model = LastMealPrep(
            days: config.days,
            selectedMeals: Array(config.selectedMeals),
            perMealConfigs: Dictionary(uniqueKeysWithValues: config.mealConfigurations.map { ($0.key.rawValue, $0.value) }),
            cuisines: config.cuisines,
            additionalRequest: config.additionalRequest.isEmpty ? nil : config.additionalRequest,
            generatedAt: Date(),
            plan: plan
        )
        if let data = try? encoder.encode(model) {
            defaults.set(data, forKey: mealPrepKey)
        }
    }

    func loadLastMealPrep() -> LastMealPrep? {
        guard let data = defaults.data(forKey: mealPrepKey) else { return nil }
        return try? decoder.decode(LastMealPrep.self, from: data)
    }

    /// Clear all cached meal plans and retention notices.
    func clearAll() {
        defaults.removeObject(forKey: quickKey)
        defaults.removeObject(forKey: mealPrepKey)
        defaults.removeObject(forKey: retentionNoticeKey)
        print("🧹 PlanStore cleared")
    }

    // MARK: - V6 Structured Save with Overlap Policy
    /// Merge a new structured plan into the last saved plan using overlap policy:
    /// - Replace non-favorited overlapping slots
    /// - Skip favorited overlapping slots
    /// - Append new slots where there is no overlap
    /// Operation is atomic (single write).
    /// Returns a summary for UI banner.
    func mergeAndSave(newPlan: MealPlan, policy: OverlapMergePolicy = .replaceNonFavorites) async -> OverlapSaveSummary {
        guard !newPlan.days.isEmpty else { return OverlapSaveSummary() }

        // Snapshot dependencies on the main actor, then hop to background work for the heavy merge.
        let existingData = defaults.data(forKey: mealPrepKey)
        let favoriteIds = FavoritesStore.shared.all()

        let result = await Task.detached(priority: .background) {
            await MergeComputationResult.compute(
                newPlan: newPlan,
                policy: policy,
                existingData: existingData,
                favoriteIds: Set(favoriteIds)
            )
        }.value

        // Use MainActor.run to ensure final write is atomic on main actor as per PERF-4 requirements
        await MainActor.run {
            if result.summary.removedWeeks > 0 {
                storeRetentionNotice(removedWeeks: result.summary.removedWeeks)
            }

            if let data = result.encodedLastMealPrep {
                defaults.set(data, forKey: mealPrepKey)
            }
        }

        return result.summary
    }


    // MARK: - Retention Notice
    private let retentionNoticeKey = "planstore.retentionNotice.v1"

    private func storeRetentionNotice(removedWeeks: Int) {
        let message = "Removed oldest week to keep the last 4 weeks. (\(removedWeeks) removed)"
        let payload: [String: Any] = [
            "message": message,
            "removedWeeks": removedWeeks,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        if let data = try? JSONSerialization.data(withJSONObject: payload) {
            defaults.set(data, forKey: retentionNoticeKey)
        }
    }

    /// Retrieve and clear the last retention notice for UI consumption.
    func takeRetentionNotice() -> (message: String, removedWeeks: Int)? {
        guard let data = defaults.data(forKey: retentionNoticeKey),
              let obj = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else { return nil }
        defaults.removeObject(forKey: retentionNoticeKey)
        let msg = obj["message"] as? String ?? "Removed oldest week to keep the last 4 weeks."
        let removed = obj["removedWeeks"] as? Int ?? 0
        return (msg, removed)
    }

    /// Replace recipes for specific MealSlot IDs in the last saved Meal Prep plan
    func replaceRecipesInLastMealPrep(_ replacements: [UUID: RecipeUIModel]) {
        guard let old = loadLastMealPrep() else { return }
        let newDays: [DayPlan] = old.plan.days.map { day in
            let newMeals = day.meals.map { slot in
                if let rep = replacements[slot.slotId] {
                    return MealSlot(slotId: slot.slotId, dayIndex: slot.dayIndex, mealType: slot.mealType, recipe: rep)
                } else {
                    return slot
                }
            }
            return DayPlan(date: day.date, meals: newMeals)
        }
        let newPlan = MealPlan(days: newDays)
        let updated = LastMealPrep(
            days: old.days,
            selectedMeals: old.selectedMeals,
            perMealConfigs: old.perMealConfigs,
            cuisines: old.cuisines,
            additionalRequest: old.additionalRequest,
            generatedAt: Date(),
            plan: newPlan
        )
        if let data = try? encoder.encode(updated) {
            defaults.set(data, forKey: mealPrepKey)
        }
    }

    /// Delete specified meal slots (by slotId) from the last saved Meal Prep plan (atomic write).
    func deleteSlotsInLastMealPrep(_ slotIds: Set<UUID>) {
        guard let old = loadLastMealPrep(), !slotIds.isEmpty else { return }
        let newDays: [DayPlan] = old.plan.days.map { day in
            let newMeals = day.meals.filter { !slotIds.contains($0.slotId) }
            return DayPlan(date: day.date, meals: newMeals)
        }
        let newPlan = MealPlan(days: newDays)
        let updated = LastMealPrep(
            days: old.days,
            selectedMeals: old.selectedMeals,
            perMealConfigs: old.perMealConfigs,
            cuisines: old.cuisines,
            additionalRequest: old.additionalRequest,
            generatedAt: Date(),
            plan: newPlan
        )
        if let data = try? encoder.encode(updated) {
            defaults.set(data, forKey: mealPrepKey)
        }
    }

}

// MARK: - Merge Computation (Background Work)

private struct MergeComputationResult: Sendable {
    let summary: OverlapSaveSummary
    let encodedLastMealPrep: Data?

    static func compute(
        newPlan: MealPlan,
        policy: OverlapMergePolicy,
        existingData: Data?,
        favoriteIds: Set<String>
    ) async -> MergeComputationResult {
        let existing: LastMealPrep?
        if let existingData {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            existing = try? decoder.decode(LastMealPrep.self, from: existingData)
        } else {
            existing = nil
        }

        if Task.isCancelled {
            return MergeComputationResult(summary: OverlapSaveSummary(), encodedLastMealPrep: nil)
        }

        var summary = OverlapSaveSummary()
        let mergedDays = mergePlans(
            oldPlan: existing?.plan,
            newPlan: newPlan,
            policy: policy,
            favoriteIds: favoriteIds,
            summary: &summary
        )

        if Task.isCancelled {
            return MergeComputationResult(summary: summary, encodedLastMealPrep: nil)
        }

        let retention = PlansRetentionManager.applyRetention(to: MealPlan(days: mergedDays))
        summary.removedWeeks = retention.removedWeeks
        let normalizedDays = normalizeDayIndicesAndDates(retention.plan.days)

        if Task.isCancelled {
            return MergeComputationResult(summary: summary, encodedLastMealPrep: nil)
        }

        let updated = LastMealPrep(
            days: normalizedDays.count,
            selectedMeals: deduceSelectedMeals(from: normalizedDays),
            perMealConfigs: existing?.perMealConfigs ?? [:],
            cuisines: existing?.cuisines ?? [],
            additionalRequest: existing?.additionalRequest,
            generatedAt: Date(),
            plan: MealPlan(days: normalizedDays)
        )

        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let encoded = try? encoder.encode(updated)

        return MergeComputationResult(summary: summary, encodedLastMealPrep: encoded)
    }

    private static func mergePlans(
        oldPlan: MealPlan?,
        newPlan: MealPlan,
        policy: OverlapMergePolicy,
        favoriteIds: Set<String>,
        summary: inout OverlapSaveSummary
    ) -> [DayPlan] {
        var oldByDate: [String: DayPlan] = [:]
        if let old = oldPlan {
            for day in old.days {
                oldByDate[dateFormatter.string(from: day.date)] = day
            }
        }

        var newByDate: [String: DayPlan] = [:]
        for day in newPlan.days {
            newByDate[dateFormatter.string(from: day.date)] = day
        }

        let allKeys = Set(oldByDate.keys).union(newByDate.keys).sorted()
        var merged: [DayPlan] = []
        merged.reserveCapacity(allKeys.count)

        for key in allKeys {
            if Task.isCancelled { return [] }
            let date = dateFormatter.date(from: key) ?? Date()
            let oldDay = oldByDate[key]
            let newDay = newByDate[key]
            let meals = mergeMeals(
                oldDay?.meals ?? [],
                newDay?.meals ?? [],
                date: date,
                policy: policy,
                favoriteIds: favoriteIds,
                summary: &summary
            )
            merged.append(DayPlan(date: date, meals: meals))
        }

        return merged
    }

    private static func mergeMeals(
        _ oldMeals: [MealSlot],
        _ newMeals: [MealSlot],
        date: Date,
        policy: OverlapMergePolicy,
        favoriteIds: Set<String>,
        summary: inout OverlapSaveSummary
    ) -> [MealSlot] {
        let oldGroups = Dictionary(grouping: oldMeals, by: { $0.mealType })
        let newGroups = Dictionary(grouping: newMeals, by: { $0.mealType })
        let mealTypes = Set(oldGroups.keys).union(newGroups.keys)
        var result: [MealSlot] = []
        result.reserveCapacity(mealTypes.count * 4)

        for meal in mealTypes {
            if Task.isCancelled { return [] }
            let oldArray = oldGroups[meal] ?? []
            let newArray = newGroups[meal] ?? []
            let merged = mergeSlotsForMeal(
                oldArray,
                newArray,
                meal: meal,
                date: date,
                policy: policy,
                favoriteIds: favoriteIds,
                summary: &summary
            )
            result.append(contentsOf: merged)
        }

        return result
    }

    private static func mergeSlotsForMeal(
        _ oldArray: [MealSlot],
        _ newArray: [MealSlot],
        meal: MealType,
        date: Date,
        policy: OverlapMergePolicy,
        favoriteIds: Set<String>,
        summary: inout OverlapSaveSummary
    ) -> [MealSlot] {
        switch policy {
        case .replaceNonFavorites:
            var result = oldArray
            var used = Array(repeating: false, count: oldArray.count)
            for newSlot in newArray {
                if Task.isCancelled { return [] }
                var replaced = false
                for index in 0..<result.count where !used[index] {
                    let slot = result[index]
                    let isFavorite = favoriteIds.contains(slot.recipe.id)
                    if !isFavorite && slot.mealType == meal {
                        let updatedRecipe = newSlot.recipe
                        let newMealSlot = MealSlot(slotId: slot.slotId, dayIndex: slot.dayIndex, mealType: meal, recipe: updatedRecipe)
                        result[index] = newMealSlot
                        used[index] = true
                        replaced = true
                        summary.replaced += 1
                        break
                    }
                }
                if !replaced {
                    result.append(MealSlot(slotId: UUID(), dayIndex: newSlot.dayIndex, mealType: meal, recipe: newSlot.recipe))
                    summary.recordAddition(on: date, meal: meal)
                    if !oldArray.isEmpty {
                        summary.skippedFavorites += 1
                    }
                }
            }
            return result
        case .appendAll:
            var result = oldArray
            result.reserveCapacity(oldArray.count + newArray.count)
            for newSlot in newArray {
                if Task.isCancelled { return [] }
                result.append(MealSlot(slotId: newSlot.slotId, dayIndex: newSlot.dayIndex, mealType: meal, recipe: newSlot.recipe))
                summary.recordAddition(on: date, meal: meal)
            }
            return result
        }
    }

    private static func normalizeDayIndicesAndDates(_ days: [DayPlan]) -> [DayPlan] {
        let sorted = days.sorted { $0.date < $1.date }
        var normalized: [DayPlan] = []
        normalized.reserveCapacity(sorted.count)

        for (index, day) in sorted.enumerated() {
            if Task.isCancelled { return [] }
            let meals = day.meals.map { slot -> MealSlot in
                let adjustedRecipe = RecipeUIModel(
                    id: slot.recipe.id,
                    title: slot.recipe.title,
                    subtitle: slot.recipe.subtitle,
                    estimatedTime: slot.recipe.estimatedTime,
                    imageURL: slot.recipe.imageURL,
                    ingredientsFromPantry: slot.recipe.ingredientsFromPantry,
                    additionalIngredients: slot.recipe.additionalIngredients,
                    difficulty: slot.recipe.difficulty,
                    mealType: slot.mealType,
                    dayIndex: index,
                    servings: slot.recipe.servings,
                    cuisine: slot.recipe.cuisine,
                    scheduledDate: day.date
                )
                return MealSlot(slotId: slot.slotId, dayIndex: index, mealType: slot.mealType, recipe: adjustedRecipe)
            }
            normalized.append(DayPlan(date: day.date, meals: meals))
        }

        return normalized
    }

    private static func deduceSelectedMeals(from days: [DayPlan]) -> [MealType] {
        var set = Set<MealType>()
        for day in days {
            if Task.isCancelled { return [] }
            for slot in day.meals {
                set.insert(slot.mealType)
            }
        }
        return Array(set).sorted { $0.rawValue < $1.rawValue }
    }

    private static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        return formatter
    }()
}

// MARK: - Models

struct LastQuick: Codable, Equatable, Sendable {
    let mealType: MealType
    let numberOfDishes: Int
    let totalCookTime: Int
    let cuisines: [String]
    let additionalRequest: String?
    let generatedAt: Date
    let recipes: [RecipeUIModel]
}

struct LastMealPrep: Codable, Equatable, Sendable {
    let days: Int
    let selectedMeals: [MealType]
    let perMealConfigs: [String: MealConfig]
    let cuisines: [String]
    let additionalRequest: String?
    let generatedAt: Date
    let plan: MealPlan
}


struct MealPlan: Codable, Equatable, Sendable {
    let days: [DayPlan]
}

struct DayPlan: Codable, Equatable, Identifiable, Sendable {
    var id: UUID { UUID() }
    let date: Date
    let meals: [MealSlot]
}

struct MealSlot: Codable, Equatable, Identifiable, Hashable, Sendable {
    let slotId: UUID
    let dayIndex: Int
    let mealType: MealType
    let recipe: RecipeUIModel
    var id: UUID { slotId }

    // Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(slotId)
    }
}

// MARK: - Builder

enum MealPlanBuilder {
    static func buildPlan(from config: CustomConfiguration, recipes: [RecipeUIModel]) -> MealPlan {
        let days = max(1, config.days)
        let selectedMeals = config.selectedMeals.isEmpty ? Set(MealType.allCases) : config.selectedMeals
        let sortedMeals = selectedMeals.sorted { $0.rawValue < $1.rawValue }

        // Compute required slots per day based on per-meal configs
        var perDaySlots: [(MealType, Int)] = []
        for meal in sortedMeals {
            let cfg = config.mealConfigurations[meal] ?? MealConfig()
            perDaySlots.append((meal, max(1, min(6, cfg.numberOfDishes))))
        }

        var recipeIterator = recipes.makeIterator()
        var dayPlans: [DayPlan] = []
        let calendar = Calendar.current
        let today = Date()

        for dayIndex in 0..<days {
            let date = calendar.date(byAdding: .day, value: dayIndex, to: today) ?? today
            var slots: [MealSlot] = []
            for (meal, count) in perDaySlots {
                for _ in 0..<count {
                    guard let r = recipeIterator.next() else { break }
                    let slot = MealSlot(slotId: UUID(), dayIndex: dayIndex, mealType: meal, recipe: r)
                    slots.append(slot)
                }
            }
            dayPlans.append(DayPlan(date: date, meals: slots))
        }
        return MealPlan(days: dayPlans)
    }
}

// MARK: - Overlap Summary

struct OverlapSaveSummary: Equatable, Sendable {
    var addedCountsByDayMeal: [Date: [MealType: Int]]
    var replaced: Int
    var skippedFavorites: Int
    var added: Int
    var removedWeeks: Int

    init(
        addedCountsByDayMeal: [Date: [MealType: Int]] = [:],
        replaced: Int = 0,
        skippedFavorites: Int = 0,
        added: Int = 0,
        removedWeeks: Int = 0
    ) {
        self.addedCountsByDayMeal = addedCountsByDayMeal
        self.replaced = replaced
        self.skippedFavorites = skippedFavorites
        self.added = added
        self.removedWeeks = removedWeeks
    }
}

private extension OverlapSaveSummary {
    mutating func recordAddition(on date: Date, meal: MealType) {
        var mealCounts = addedCountsByDayMeal[date] ?? [:]
        mealCounts[meal, default: 0] += 1
        addedCountsByDayMeal[date] = mealCounts
        added += 1
    }
}
